// 淘宝商品信息提取插件
(function() {
    'use strict';
    
    // 存储键名
    const STORAGE_KEY = 'taobao_product_data';
    const CARD_STORAGE_KEY = 'taobao_card_data';

    // 全局变量
    let extractedProducts = []; // 存储提取的产品列表
    let cardDataArray = []; // 存储手卡数据
    
    // MD5哈希函数实现
    function md5(string) {
        function RotateLeft(lValue, iShiftBits) {
            return (lValue<<iShiftBits) | (lValue>>>(32-iShiftBits));
        }
        function AddUnsigned(lX,lY) {
            var lX4,lY4,lX8,lY8,lResult;
            lX8 = (lX & 0x80000000);
            lY8 = (lY & 0x80000000);
            lX4 = (lX & 0x40000000);
            lY4 = (lY & 0x40000000);
            lResult = (lX & 0x3FFFFFFF)+(lY & 0x3FFFFFFF);
            if (lX4 & lY4) {
                return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
            }
            if (lX4 | lY4) {
                if (lResult & 0x40000000) {
                    return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
                } else {
                    return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
                }
            } else {
                return (lResult ^ lX8 ^ lY8);
            }
        }
        function F(x,y,z) { return (x & y) | ((~x) & z); }
        function G(x,y,z) { return (x & z) | (y & (~z)); }
        function H(x,y,z) { return (x ^ y ^ z); }
        function I(x,y,z) { return (y ^ (x | (~z))); }
        function FF(a,b,c,d,x,s,ac) {
            a = AddUnsigned(a, AddUnsigned(AddUnsigned(F(b, c, d), x), ac));
            return AddUnsigned(RotateLeft(a, s), b);
        };
        function GG(a,b,c,d,x,s,ac) {
            a = AddUnsigned(a, AddUnsigned(AddUnsigned(G(b, c, d), x), ac));
            return AddUnsigned(RotateLeft(a, s), b);
        };
        function HH(a,b,c,d,x,s,ac) {
            a = AddUnsigned(a, AddUnsigned(AddUnsigned(H(b, c, d), x), ac));
            return AddUnsigned(RotateLeft(a, s), b);
        };
        function II(a,b,c,d,x,s,ac) {
            a = AddUnsigned(a, AddUnsigned(AddUnsigned(I(b, c, d), x), ac));
            return AddUnsigned(RotateLeft(a, s), b);
        };
        function ConvertToWordArray(string) {
            var lWordCount;
            var lMessageLength = string.length;
            var lNumberOfWords_temp1=lMessageLength + 8;
            var lNumberOfWords_temp2=(lNumberOfWords_temp1-(lNumberOfWords_temp1 % 64))/64;
            var lNumberOfWords = (lNumberOfWords_temp2+1)*16;
            var lWordArray=Array(lNumberOfWords-1);
            var lBytePosition = 0;
            var lByteCount = 0;
            while ( lByteCount < lMessageLength ) {
                lWordCount = (lByteCount-(lByteCount % 4))/4;
                lBytePosition = (lByteCount % 4)*8;
                lWordArray[lWordCount] = (lWordArray[lWordCount] | (string.charCodeAt(lByteCount)<<lBytePosition));
                lByteCount++;
            }
            lWordCount = (lByteCount-(lByteCount % 4))/4;
            lBytePosition = (lByteCount % 4)*8;
            lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80<<lBytePosition);
            lWordArray[lNumberOfWords-2] = lMessageLength<<3;
            lWordArray[lNumberOfWords-1] = lMessageLength>>>29;
            return lWordArray;
        };
        function WordToHex(lValue) {
            var WordToHexValue="",WordToHexValue_temp="",lByte,lCount;
            for (lCount = 0;lCount<=3;lCount++) {
                lByte = (lValue>>>(lCount*8)) & 255;
                WordToHexValue_temp = "0" + lByte.toString(16);
                WordToHexValue = WordToHexValue + WordToHexValue_temp.substring(WordToHexValue_temp.length-2);
            }
            return WordToHexValue;
        };
        function Utf8Encode(string) {
            string = string.replace(/\r\n/g,"\n");
            var utftext = "";
            for (var n = 0; n < string.length; n++) {
                var c = string.charCodeAt(n);
                if (c < 128) {
                    utftext += String.fromCharCode(c);
                }
                else if((c > 127) && (c < 2048)) {
                    utftext += String.fromCharCode((c >> 6) | 192);
                    utftext += String.fromCharCode((c & 63) | 128);
                }
                else {
                    utftext += String.fromCharCode((c >> 12) | 224);
                    utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                    utftext += String.fromCharCode((c & 63) | 128);
                }
            }
            return utftext;
        };
        var x=Array();
        var k,AA,BB,CC,DD,a,b,c,d;
        var S11=7, S12=12, S13=17, S14=22;
        var S21=5, S22=9 , S23=14, S24=20;
        var S31=4, S32=11, S33=16, S34=23;
        var S41=6, S42=10, S43=15, S44=21;
        string = Utf8Encode(string);
        x = ConvertToWordArray(string);
        a = 0x67452301; b = 0xEFCDAB89; c = 0x98BADCFE; d = 0x10325476;
        for (k=0;k<x.length;k+=16) {
            AA=a; BB=b; CC=c; DD=d;
            a=FF(a,b,c,d,x[k+0], S11,0xD76AA478);
            d=FF(d,a,b,c,x[k+1], S12,0xE8C7B756);
            c=FF(c,d,a,b,x[k+2], S13,0x242070DB);
            b=FF(b,c,d,a,x[k+3], S14,0xC1BDCEEE);
            a=FF(a,b,c,d,x[k+4], S11,0xF57C0FAF);
            d=FF(d,a,b,c,x[k+5], S12,0x4787C62A);
            c=FF(c,d,a,b,x[k+6], S13,0xA8304613);
            b=FF(b,c,d,a,x[k+7], S14,0xFD469501);
            a=FF(a,b,c,d,x[k+8], S11,0x698098D8);
            d=FF(d,a,b,c,x[k+9], S12,0x8B44F7AF);
            c=FF(c,d,a,b,x[k+10],S13,0xFFFF5BB1);
            b=FF(b,c,d,a,x[k+11],S14,0x895CD7BE);
            a=FF(a,b,c,d,x[k+12],S11,0x6B901122);
            d=FF(d,a,b,c,x[k+13],S12,0xFD987193);
            c=FF(c,d,a,b,x[k+14],S13,0xA679438E);
            b=FF(b,c,d,a,x[k+15],S14,0x49B40821);
            a=GG(a,b,c,d,x[k+1], S21,0xF61E2562);
            d=GG(d,a,b,c,x[k+6], S22,0xC040B340);
            c=GG(c,d,a,b,x[k+11],S23,0x265E5A51);
            b=GG(b,c,d,a,x[k+0], S24,0xE9B6C7AA);
            a=GG(a,b,c,d,x[k+5], S21,0xD62F105D);
            d=GG(d,a,b,c,x[k+10],S22,0x2441453);
            c=GG(c,d,a,b,x[k+15],S23,0xD8A1E681);
            b=GG(b,c,d,a,x[k+4], S24,0xE7D3FBC8);
            a=GG(a,b,c,d,x[k+9], S21,0x21E1CDE6);
            d=GG(d,a,b,c,x[k+14],S22,0xC33707D6);
            c=GG(c,d,a,b,x[k+3], S23,0xF4D50D87);
            b=GG(b,c,d,a,x[k+8], S24,0x455A14ED);
            a=GG(a,b,c,d,x[k+13],S21,0xA9E3E905);
            d=GG(d,a,b,c,x[k+2], S22,0xFCEFA3F8);
            c=GG(c,d,a,b,x[k+7], S23,0x676F02D9);
            b=GG(b,c,d,a,x[k+12],S24,0x8D2A4C8A);
            a=HH(a,b,c,d,x[k+5], S31,0xFFFA3942);
            d=HH(d,a,b,c,x[k+8], S32,0x8771F681);
            c=HH(c,d,a,b,x[k+11],S33,0x6D9D6122);
            b=HH(b,c,d,a,x[k+14],S34,0xFDE5380C);
            a=HH(a,b,c,d,x[k+1], S31,0xA4BEEA44);
            d=HH(d,a,b,c,x[k+4], S32,0x4BDECFA9);
            c=HH(c,d,a,b,x[k+7], S33,0xF6BB4B60);
            b=HH(b,c,d,a,x[k+10],S34,0xBEBFBC70);
            a=HH(a,b,c,d,x[k+13],S31,0x289B7EC6);
            d=HH(d,a,b,c,x[k+0], S32,0xEAA127FA);
            c=HH(c,d,a,b,x[k+3], S33,0xD4EF3085);
            b=HH(b,c,d,a,x[k+6], S34,0x4881D05);
            a=HH(a,b,c,d,x[k+9], S31,0xD9D4D039);
            d=HH(d,a,b,c,x[k+12],S32,0xE6DB99E5);
            c=HH(c,d,a,b,x[k+15],S33,0x1FA27CF8);
            b=HH(b,c,d,a,x[k+2], S34,0xC4AC5665);
            a=II(a,b,c,d,x[k+0], S41,0xF4292244);
            d=II(d,a,b,c,x[k+7], S42,0x432AFF97);
            c=II(c,d,a,b,x[k+14],S43,0xAB9423A7);
            b=II(b,c,d,a,x[k+5], S44,0xFC93A039);
            a=II(a,b,c,d,x[k+12],S41,0x655B59C3);
            d=II(d,a,b,c,x[k+3], S42,0x8F0CCC92);
            c=II(c,d,a,b,x[k+10],S43,0xFFEFF47D);
            b=II(b,c,d,a,x[k+1], S44,0x85845DD1);
            a=II(a,b,c,d,x[k+8], S41,0x6FA87E4F);
            d=II(d,a,b,c,x[k+15],S42,0xFE2CE6E0);
            c=II(c,d,a,b,x[k+6], S43,0xA3014314);
            b=II(b,c,d,a,x[k+13],S44,0x4E0811A1);
            a=II(a,b,c,d,x[k+4], S41,0xF7537E82);
            d=II(d,a,b,c,x[k+11],S42,0xBD3AF235);
            c=II(c,d,a,b,x[k+2], S43,0x2AD7D2BB);
            b=II(b,c,d,a,x[k+9], S44,0xEB86D391);
            a=AddUnsigned(a,AA);
            b=AddUnsigned(b,BB);
            c=AddUnsigned(c,CC);
            d=AddUnsigned(d,DD);
        }
        return (WordToHex(a)+WordToHex(b)+WordToHex(c)+WordToHex(d)).toLowerCase();
    }

    // 获取h5_tk token用于签名
    function getH5Token() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === '_m_h5_tk') {
                return value.split('_')[0];
            }
        }
        return "";
    }
    
    // 初始化插件
    function initPlugin() {
        createPluginModal();
        loadStoredData();

        // 添加全局函数到window对象，供HTML调用
        window.closeCardModal = closeCardModal;
    }
    
    // 创建插件Modal
    function createPluginModal() {
        // 获取当前liveId
        const urlParams = new URLSearchParams(window.location.search);
        const liveId = urlParams.get('liveId');
        const liveIdDisplay = liveId ? `直播间: ${liveId}` : '未检测到直播间ID';
        
        // 获取保存的位置
        const savedPosition = getSavedPosition();
        
        // 创建modal容器
        const modal = document.createElement('div');
        modal.id = 'taobao-plugin-modal';
        modal.innerHTML = `
            <div class="plugin-header" id="plugin-header">
                <span class="plugin-title">
                    <span class="drag-icon">⋮⋮</span>
                    淘宝产品手卡提取器
                </span>
                <div class="plugin-stats">
                    <div id="live-id-display">${liveIdDisplay} <span id="product-count">已提取: 0 个商品</span></div>
                   
                </div>
            </div>
            <div class="plugin-content">
                <div class="plugin-buttons">
                    <button id="extract-btn" class="plugin-btn extract-btn">提取数据</button>
                    <button id="export-btn" class="plugin-btn export-btn">导出Excel</button>
                    <button id="clear-btn" class="plugin-btn clear-btn">清空数据</button>
                </div>

                <!-- 批量手卡提取区域 -->
                <div class="batch-card-section">
                    <div class="section-title">批量AI手卡提取</div>
                    <div class="batch-controls-inline">
                        <div class="control-group">
                            <label>序号区间:</label>
                            <div class="range-inputs">
                                <input type="number" id="start-index" min="1" value="1" placeholder="起始">
                                <span class="range-separator">-</span>
                                <input type="number" id="end-index" min="1" value="10" placeholder="结束">
                            </div>
                        </div>
                        <div class="control-group">
                            <label for="batch-size">并发数:</label>
                            <input type="number" id="batch-size" min="1" max="100" value="20" class="batch-size-input">
                        </div>
                        <button id="quick-setup-btn" class="plugin-btn quick-setup-btn">一键设置</button>
                        <button id="batch-extract-btn" class="plugin-btn batch-btn">批量提取(分批)</button>
                        <button id="batch-extract-pool-btn" class="plugin-btn batch-btn">批量提取(线程池)</button>
                    </div>

                    <!-- 提取列表 -->
                    <div class="extract-list-container">
                        <div class="list-header">
                            <span class="header-title">产品序号列表</span>
                            <div class="header-right">
                                <span id="extract-progress">0/0</span>
                                <button id="retry-failed-btn" class="plugin-btn retry-btn" style="display: none;">重新提取失败</button>
                            </div>
                        </div>
                        <div id="extract-grid" class="extract-grid"></div>
                    </div>
                </div>
            </div>
        `;
        
        // 应用保存的位置
        modal.style.position = 'fixed';
        modal.style.left = savedPosition.left;
        modal.style.top = savedPosition.top;
        modal.style.transform = 'none'; // 取消居中变换
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #taobao-plugin-modal {
                width: 500px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                z-index: 999999;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                color: white;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                user-select: none;
                transition: box-shadow 0.3s ease;
            }
            
            #taobao-plugin-modal.dragging {
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
                transform: scale(1.02);
            }
            
            .plugin-header {
                padding: 15px 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
                position: relative;
            }
            
            .plugin-header:hover {
                background: rgba(255, 255, 255, 0.1);
            }
            
            .plugin-title {
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .drag-icon {
                font-size: 14px;
                opacity: 0.7;
                transform: rotate(90deg);
                transition: opacity 0.3s ease;
            }
            
            .plugin-header:hover .drag-icon {
                opacity: 1;
            }
            
            .plugin-stats {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 4px;
            }
            
            .plugin-stats > div, .plugin-stats > span {
                font-size: 11px;
                background: rgba(255, 255, 255, 0.2);
                padding: 3px 10px;
                border-radius: 15px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            
            .plugin-content {
                padding: 20px;
            }
            
            .plugin-buttons {
                display: flex;
                gap: 12px;
            }
            
            .plugin-btn {
                flex: 1;
                padding: 10px 16px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            
            .extract-btn {
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
            }
            
            .export-btn {
                background: linear-gradient(135deg, #2196F3, #1976D2);
                color: white;
            }
            
            .clear-btn {
                background: linear-gradient(135deg, #f44336, #d32f2f);
                color: white;
            }
            
            .plugin-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
            
            .plugin-btn:active {
                transform: translateY(0);
            }
            
            .plugin-btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }

            /* 批量手卡提取样式 */
            .batch-card-section {
                margin-top: 20px;
                padding-top: 20px;
                border-top: 1px solid rgba(255, 255, 255, 0.2);
            }

            .section-title {
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 15px;
                color: rgba(255, 255, 255, 0.9);
            }

            .batch-controls {
                display: flex;
                gap: 15px;
                align-items: center;
                margin-bottom: 15px;
                flex-wrap: wrap;
            }

            .range-group {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .range-group label {
                font-size: 11px;
                color: rgba(255, 255, 255, 0.8);
                white-space: nowrap;
            }

            .range-inputs {
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .range-inputs input {
                width: 70px;
                padding: 6px 8px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.1);
                color: white;
                font-size: 12px;
                text-align: center;
            }

            .range-inputs input:focus {
                outline: none;
                border-color: #4CAF50;
                background: rgba(255, 255, 255, 0.15);
            }

            .range-separator {
                color: rgba(255, 255, 255, 0.8);
                font-weight: bold;
                font-size: 12px;
            }

            .input-group {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .input-group label {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
                white-space: nowrap;
            }

            .input-group input {
                flex: 1;
                padding: 6px 10px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.1);
                color: white;
                font-size: 12px;
            }

            .input-group input::placeholder {
                color: rgba(255, 255, 255, 0.5);
            }

            /* 新的一行布局样式 */
            .batch-controls-inline {
                display: flex;
                align-items: center;
                gap: 15px;
                margin-top: 15px;
                flex-wrap: wrap;
            }

            .control-group {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .control-group label {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
                white-space: nowrap;
            }

            .batch-size-input {
                width: 50px !important;
                padding: 6px 8px !important;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.1);
                color: white;
                font-size: 12px;
                text-align: center;
            }

            .batch-size-input:focus {
                outline: none;
                border-color: #4CAF50;
                background: rgba(255, 255, 255, 0.15);
            }

            .quick-setup-btn {
                padding: 6px 10px;
                font-size: 11px;
                background: linear-gradient(135deg, #ff6b35, #f7931e);
                border: none;
                border-radius: 4px;
                color: white;
                cursor: pointer;
                transition: all 0.2s ease;
                white-space: nowrap;
            }

            .quick-setup-btn:hover {
                background: linear-gradient(135deg, #ff8c5a, #ffa940);
                transform: translateY(-1px);
            }

            .batch-btn {
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
                white-space: nowrap;
                padding: 6px 12px;
                font-size: 12px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .batch-btn:hover {
                background: linear-gradient(135deg, #5cbf60, #4caf50);
                transform: translateY(-1px);
            }

            .extract-list-container {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                overflow: hidden;
                margin-top: 25px;
            }

            .list-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 15px;
                background: rgba(255, 255, 255, 0.1);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                font-size: 12px;
                font-weight: bold;
            }

            .header-title {
                color: rgba(255, 255, 255, 0.8);
            }

            .header-right {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .retry-btn {
                padding: 4px 8px;
                font-size: 10px;
                background: #ff6b35;
                border: none;
                border-radius: 3px;
                color: white;
                cursor: pointer;
            }

            .retry-btn:hover {
                background: #e55a2b;
            }

            .extract-grid {
                display: grid;
                grid-template-columns: repeat(10, 1fr);
                gap: 6px;
                padding: 15px;
                max-height: 400px;
                overflow-y: auto;
            }

            .extract-grid::-webkit-scrollbar {
                width: 6px;
            }

            .extract-grid::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }

            .extract-grid::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 3px;
            }

            .extract-grid::-webkit-scrollbar-thumb:hover {
                background: rgba(255, 255, 255, 0.5);
            }

            .grid-item {
                width: 30px;
                height: 30px;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 11px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }

            .grid-item.status-waiting {
                background: rgba(255, 255, 255, 0.2);
                color: white;
            }

            .grid-item.status-processing {
                background: #ff9500;
                color: white;
                animation: pulse 1.5s infinite;
            }

            .grid-item.status-success {
                background: #4CAF50;
                color: white;
            }

            .grid-item.status-success:hover {
                background: #45a049;
                transform: scale(1.1);
            }

            .grid-item.status-failed {
                background: #f44336;
                color: white;
            }

            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.7; }
                100% { opacity: 1; }
            }

            /* 手卡显示Modal */
            .card-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 999999;
            }

            .card-modal-content {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 10px;
                padding: 20px;
                max-width: 600px;
                max-height: 80vh;
                overflow-y: auto;
                position: relative;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            }

            .card-modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            }

            .card-modal-title {
                color: white;
                font-size: 16px;
                font-weight: bold;
            }

            .card-modal-close {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.5);
                color: white;
                font-size: 20px;
                font-weight: bold;
                cursor: pointer;
                padding: 0;
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.2s ease;
            }

            .card-modal-close:hover {
                background: rgba(255, 255, 255, 0.3);
                border-color: rgba(255, 255, 255, 0.8);
                transform: scale(1.1);
            }

            .card-content {
                color: white;
                line-height: 1.6;
                font-size: 14px;
                white-space: pre-wrap;
                word-wrap: break-word;
            }

            .item-status {
                padding: 2px 8px;
                border-radius: 10px;
                font-size: 10px;
                font-weight: bold;
                white-space: nowrap;
            }

            .status-waiting {
                background: rgba(255, 193, 7, 0.3);
                color: #FFC107;
            }

            .status-processing {
                background: rgba(33, 150, 243, 0.3);
                color: #2196F3;
            }

            .status-success {
                background: rgba(76, 175, 80, 0.3);
                color: #4CAF50;
            }

            .status-failed {
                background: rgba(244, 67, 54, 0.3);
                color: #f44336;
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(modal);
        
        // 初始化拖动功能
        initDragFunctionality(modal);
        
        // 如果没有liveId，禁用提取按钮
        if (!liveId) {
            document.getElementById('extract-btn').disabled = true;
        }
        
        // 绑定事件
        bindEvents();
    }
    
    // 获取保存的位置
    function getSavedPosition() {
        try {
            const saved = localStorage.getItem('taobao_plugin_position');
            if (saved) {
                const position = JSON.parse(saved);
                // 验证位置是否在屏幕范围内
                const maxX = window.innerWidth - 500; // 减去插件宽度
                const maxY = window.innerHeight - 200; // 减去插件高度的估计值
                
                return {
                    left: Math.max(0, Math.min(position.left, maxX)) + 'px',
                    top: Math.max(0, Math.min(position.top, maxY)) + 'px'
                };
            }
        } catch (error) {
            console.error('获取保存位置失败:', error);
        }
        
        // 默认位置（顶部中间）
        return {
            left: Math.max(0, (window.innerWidth - 500) / 2) + 'px',
            top: '20px'
        };
    }
    
    // 保存位置
    function savePosition(left, top) {
        try {
            const position = {
                left: parseInt(left),
                top: parseInt(top)
            };
            localStorage.setItem('taobao_plugin_position', JSON.stringify(position));
        } catch (error) {
            console.error('保存位置失败:', error);
        }
    }
    
    // 初始化拖动功能
    function initDragFunctionality(modal) {
        const header = modal.querySelector('#plugin-header');
        let isDragging = false;
        let startX, startY, initialLeft, initialTop;
        
        header.addEventListener('mousedown', function(e) {
            isDragging = true;
            modal.classList.add('dragging');
            
            startX = e.clientX;
            startY = e.clientY;
            
            const rect = modal.getBoundingClientRect();
            initialLeft = rect.left;
            initialTop = rect.top;
            
            // 防止文本选择
            e.preventDefault();
            
            // 添加全局鼠标事件监听器
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        });
        
        function onMouseMove(e) {
            if (!isDragging) return;
            
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            let newLeft = initialLeft + deltaX;
            let newTop = initialTop + deltaY;
            
            // 限制在屏幕范围内
            const maxLeft = window.innerWidth - modal.offsetWidth;
            const maxTop = window.innerHeight - modal.offsetHeight;
            
            newLeft = Math.max(0, Math.min(newLeft, maxLeft));
            newTop = Math.max(0, Math.min(newTop, maxTop));
            
            modal.style.left = newLeft + 'px';
            modal.style.top = newTop + 'px';
        }
        
        function onMouseUp() {
            if (isDragging) {
                isDragging = false;
                modal.classList.remove('dragging');
                
                // 保存新位置
                const rect = modal.getBoundingClientRect();
                savePosition(rect.left, rect.top);
                
                // 移除全局事件监听器
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
            }
        }
        
        // 处理窗口大小变化
        window.addEventListener('resize', function() {
            const rect = modal.getBoundingClientRect();
            const maxLeft = window.innerWidth - modal.offsetWidth;
            const maxTop = window.innerHeight - modal.offsetHeight;
            
            if (rect.left > maxLeft || rect.top > maxTop) {
                const newLeft = Math.max(0, Math.min(rect.left, maxLeft));
                const newTop = Math.max(0, Math.min(rect.top, maxTop));
                
                modal.style.left = newLeft + 'px';
                modal.style.top = newTop + 'px';
                
                savePosition(newLeft, newTop);
            }
        });
    }
    
    // 绑定事件
    function bindEvents() {
        document.getElementById('extract-btn').addEventListener('click', extractData);
        document.getElementById('export-btn').addEventListener('click', exportData);
        document.getElementById('clear-btn').addEventListener('click', clearData);
        document.getElementById('batch-extract-btn').addEventListener('click', startBatchExtract);
        document.getElementById('batch-extract-pool-btn').addEventListener('click', startBatchExtractWithPool);
        document.getElementById('retry-failed-btn').addEventListener('click', retryFailedExtraction);
        document.getElementById('quick-setup-btn').addEventListener('click', quickSetup);
    }
    
    // 提取数据
    function extractData() {
        showLoading();
        
        // 从当前URL获取liveId参数
        const urlParams = new URLSearchParams(window.location.search);
        const liveId = urlParams.get('liveId');
        
        if (!liveId) {
            layer.msg('未找到liveId参数，请确保在直播编辑页面使用');
            hideLoading();
            return;
        }
        
        // 获取h5_tk token
        const h5Token = getH5Token();
        if (!h5Token) {
            layer.msg('未找到h5_tk token，请确保已登录淘宝');
            hideLoading();
            return;
        }
        
        // 构建请求参数
        const timestamp = Date.now();
        const appKey = '12574478';
        const api = 'mtop.taobao.dreamweb.live.item.all.query';
        const dataStr = JSON.stringify({
            "liveId": liveId,
            "searchType": "all",
            "pageNum": 1,
            "anchorSource": "pc"
        });
        
        // 生成签名
        const signString = `${h5Token}&${timestamp}&${appKey}&${dataStr}`;
        const sign = md5(signString);
        
        // 构建请求URL和参数
        const baseUrl = 'https://h5api.m.taobao.com/h5/mtop.taobao.dreamweb.live.item.all.query/1.0/';
        const params = new URLSearchParams({
            jsv: '2.7.4',
            appKey: appKey,
            t: timestamp,
            sign: sign,
            api: api,
            jsonpIncPrefix: 'tbla_commodity_list',
            v: '1.0',
            preventFallback: 'true',
            type: 'jsonp',
            dataType: 'jsonp',
            callback: 'mtopjsonptbla_commodity_list' + Math.floor(Math.random() * 100),
            data: dataStr
        });
        
        const requestUrl = baseUrl + '?' + params.toString();
        

        
        // 创建JSONP请求
        makeJsonpRequest(requestUrl)
            .then(data => {
                processResponseData(data);
            })
            .catch(error => {
                console.error('提取失败:', error);
                layer.msg('数据提取失败: ' + error.message);
                hideLoading();
            });
    }
    
    // 创建JSONP请求
    function makeJsonpRequest(url) {
        return new Promise((resolve, reject) => {
            // 提取callback函数名
            const callbackMatch = url.match(/callback=([^&]*)/);
            const callbackName = callbackMatch ? callbackMatch[1] : 'mtopjsonptbla_commodity_list' + Math.floor(Math.random() * 100);
            

            
            // 使用fetch请求，设置完整的headers
            fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': '*/*',
                    'Accept-Encoding': 'gzip, deflate, br, zstd',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Referer': 'https://liveplatform.taobao.com/',
                    'Sec-Ch-Ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                    'Sec-Ch-Ua-Mobile': '?0',
                    'Sec-Ch-Ua-Platform': '"Windows"',
                    'Sec-Fetch-Dest': 'script',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'same-site',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                },
                credentials: 'include', // 确保携带cookie
                mode: 'cors',
                cache: 'no-cache'
            })
            .then(response => {

                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(text => {

                
                try {
                    // 处理JSONP响应，提取JSON数据
                    // 查找callback函数调用的模式 - 更宽松的匹配
                    const jsonpPatterns = [
                        new RegExp(`${callbackName}\\s*\\((.*)\\)\\s*;?\\s*$`, 's'),
                        new RegExp(`${callbackName}\\s*\\((.+)\\)`, 's'),
                        /(\{.*\})/s // 直接查找JSON对象
                    ];
                    
                    let jsonData = null;
                    
                    for (const pattern of jsonpPatterns) {
                        const match = text.match(pattern);
                        if (match && match[1]) {
                            try {
                                jsonData = JSON.parse(match[1]);

                                break;
                            } catch (e) {
                                console.warn('解析失败，尝试下一个模式:', e.message);
                                continue;
                            }
                        }
                    }
                    
                    if (jsonData) {
                        resolve(jsonData);
                    } else {
                        throw new Error('无法解析JSONP响应格式');
                    }
                } catch (parseError) {
                    console.error('响应解析失败:', parseError);
                    console.error('响应内容:', text);
                    reject(new Error('响应格式错误: ' + parseError.message));
                }
            })
            .catch(error => {
                console.error('网络请求失败:', error);
                
                // 如果fetch失败，回退到传统的JSONP方式

                fallbackToScriptTag(url, callbackName, resolve, reject);
            });
        });
    }
    
    // 回退的传统JSONP实现
    function fallbackToScriptTag(url, callbackName, resolve, reject) {
        const script = document.createElement('script');
        
        // 修改URL中的callback参数
        url = url.replace(/callback=[^&]*/, 'callback=' + callbackName);
        
        // 定义全局回调函数
        window[callbackName] = function(data) {
            resolve(data);
            cleanup();
        };
        
        script.onerror = function() {
            reject(new Error('网络请求失败'));
            cleanup();
        };
        
        function cleanup() {
            if (script && script.parentNode) {
                script.parentNode.removeChild(script);
            }
            if (window[callbackName]) {
                delete window[callbackName];
            }
        }
        
        script.src = url;
        document.head.appendChild(script);
        
        // 10秒超时
        setTimeout(() => {
            if (window[callbackName]) {
                reject(new Error('请求超时'));
                cleanup();
            }
        }, 10000);
    }
    
    // 处理响应数据
    function processResponseData(data) {
        try {
            if (!data || !data.data || !data.data.itemList) {
                throw new Error('响应数据格式错误');
            }
            
            const items = data.data.itemList;
            const extractedData = [];
            
            items.forEach(item => {
                const extractedItem = {
                    goodsIndex: item.goodsIndex,
                    itemId: item.itemId,
                    itemName: item.itemName,
                    itemPrice: item.itemPrice,
                    tcpCommission: item.extendVal?.tcpCommission || '',
                    tcpCommissionType: item.extendVal?.tcpCommissionType || '',
                    subTitle: item.extendVal?.subTitle || '',
                    categoryLevelOneName: item.extendVal?.categoryLevelOneName || '',
                    extractTime: new Date().toLocaleString()
                };
                extractedData.push(extractedItem);
            });
            
            // 保存到本地存储
            saveToLocalStorage(extractedData);
            
            layer.msg(`成功提取 ${extractedData.length} 个商品信息`);
            hideLoading();
            
        } catch (error) {
            console.error('数据处理失败:', error);
            layer.msg('数据处理失败: ' + error.message);
            hideLoading();
        }
    }
    
    // 保存到本地存储
    function saveToLocalStorage(newData) {
        try {
            const existingData = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
            
            // 合并数据（避免重复）
            const allData = [...existingData];
            newData.forEach(newItem => {
                const exists = allData.find(item => item.itemId === newItem.itemId);
                if (!exists) {
                    allData.push(newItem);
                }
            });
            
            localStorage.setItem(STORAGE_KEY, JSON.stringify(allData));
            updateProductCount();
        } catch (error) {
            console.error('保存失败:', error);
            layer.msg('保存失败: ' + error.message);
        }
    }
    
    // 导出数据
    function exportData() {
        try {
            const productData = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
            const cardDataObject = JSON.parse(localStorage.getItem(CARD_STORAGE_KEY) || '{}');
            const cardData = Object.values(cardDataObject); // 转换为数组

            if (productData.length === 0 && cardData.length === 0) {
                layer.msg('暂无数据可导出', {icon: 3});
                return;
            }

            // 创建工作簿
            const wb = XLSX.utils.book_new();

            // 如果有商品数据，创建商品信息工作表
            if (productData.length > 0) {
                const productExportData = productData.map(item => ({
                    '商品序号': item.goodsIndex,
                    '商品ID': item.itemId,
                     '链接': `https://item.taobao.com/item.htm?id=${item.itemId}`,
                    '商品名称': item.itemName,
                    '商品副标题': item.subTitle,
                    '商品价格': item.itemPrice,
                    '佣金比例': item.tcpCommission,
                    '佣金类型': item.tcpCommissionType,
                    '商品类目': item.categoryLevelOneName,
                    '提取时间': item.extractTime
                }));

                const productWs = XLSX.utils.json_to_sheet(productExportData);
                const productColWidths = [
                    {wch: 10}, // 商品序号
                    {wch: 15}, // 商品ID
                    {wch: 50}, // 链接
                    {wch: 50}, // 商品名称
                    {wch: 30}, // 商品副标题
                    {wch: 12}, // 商品价格
                    {wch: 12}, // 佣金比例
                    {wch: 12}, // 佣金类型
                    {wch: 20}, // 商品类目
                    {wch: 20}  // 提取时间
                ];
                productWs['!cols'] = productColWidths;
                XLSX.utils.book_append_sheet(wb, productWs, '商品信息');
            }

            // 如果有手卡数据，创建手卡信息工作表
            if (cardData.length > 0) {
                const cardExportData = cardData.map(item => {
                    // 从产品列表中查找对应的产品信息
                    const productInfo = productData.find(p => p.itemId === item.itemId) || {};

                    // 处理手卡内容，去掉【】和序号
                    const processedScript = item.script ?
                        item.script
                            .replace(/【[^】]*】/g, '') // 去掉【xx】
                            .replace(/^\d+\.\s*/gm, '') // 去掉行首的数字序号（如"1. "）
                            .replace(/\n+/g, ' ') // 将换行符替换为空格
                            .trim() // 去掉首尾空格
                        : '';

                    return {
                        '商品序号': productInfo.goodsIndex,
                        '商品ID': item.itemId,
                        '商品标题': productInfo.itemName,
                        '商品副标题': productInfo.subTitle,
                        '商品价格': productInfo.itemPrice,
                        '佣金比例': productInfo.tcpCommission,
                        '佣金类型': productInfo.tcpCommissionType,
                        'AI手卡内容': item.script,
                        '处理后文本': processedScript,
                        '提取时间': item.extractTime
                    };
                });

                const cardWs = XLSX.utils.json_to_sheet(cardExportData);
                const cardColWidths = [
                    {wch: 10}, // 商品序号
                    {wch: 15}, // 商品ID
                    {wch: 50}, // 商品标题
                    {wch: 30}, // 商品副标题
                    {wch: 12}, // 商品价格
                    {wch: 12}, // 佣金比例
                    {wch: 12}, // 佣金类型
                    {wch: 100}, // AI手卡内容
                    {wch: 80}, // 处理后文本
                    {wch: 20}  // 提取时间
                ];
                cardWs['!cols'] = cardColWidths;
                XLSX.utils.book_append_sheet(wb, cardWs, 'AI手卡信息');
            }

            // 导出文件
            const fileName = `淘宝直播产品数据_${new Date().getTime()}.xlsx`;
            XLSX.writeFile(wb, fileName);

            const totalCount = productData.length + cardData.length;
            layer.msg(`成功导出 ${totalCount} 条数据 (商品:${productData.length}, 手卡:${cardData.length})`);

        } catch (error) {
            console.error('导出失败:', error);
            layer.msg('导出失败: ' + error.message);
        }
    }
    
    // 清空数据
    function clearData() {
        layer.confirm('确定要清空所有数据吗？（包括商品数据和手卡数据）', {
            btn: ['确定', '取消'],
            icon: 3,
            title: '确认清空'
        }, function(index) {
            localStorage.removeItem(STORAGE_KEY);
            localStorage.removeItem(CARD_STORAGE_KEY);
            cardDataArray = [];
            extractedProducts = [];
            updateProductCount();

            // 清空提取列表显示
            const listContainer = document.getElementById('extract-list');
            const gridContainer = document.getElementById('extract-grid');
            const progressElement = document.getElementById('extract-progress');
            const retryBtn = document.getElementById('retry-failed-btn');

            if (listContainer) listContainer.innerHTML = '';
            if (gridContainer) gridContainer.innerHTML = '';
            if (progressElement) progressElement.textContent = '0/0';
            if (retryBtn) retryBtn.style.display = 'none';

            layer.msg('所有数据已清空');
            layer.close(index);
        });
    }
    
    // 显示加载状态
    function showLoading() {
        layer.load(1, {
            shade: [0.3, '#000']
        });
    }
    
    // 隐藏加载状态
    function hideLoading() {
        layer.closeAll('loading');
    }
    
    // 更新商品数量显示
    function updateProductCount() {
        try {
            const productData = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
            const cardDataObject = JSON.parse(localStorage.getItem(CARD_STORAGE_KEY) || '{}');
            const cardCount = Object.keys(cardDataObject).length;
            document.getElementById('product-count').textContent = `已提取: ${productData.length} 个商品, ${cardCount} 个手卡`;
        } catch (error) {
            console.error('更新计数失败:', error);
        }
    }
    
    // 更新产品状态的通用函数
    function updateProductStatus(product, status, gridElement = null) {
        // 更新内存中的产品状态
        product.cardStatus = status;
        if (status === 'success') {
            product.extractTime = new Date().toLocaleString();
        }

        // 更新UI状态
        if (gridElement) {
            gridElement.className = `grid-item status-${status}`;
        }

        // 更新localStorage中的产品数据
        const storedProducts = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
        const productIndex = storedProducts.findIndex(p => p.itemId === product.itemId);
        if (productIndex !== -1) {
            storedProducts[productIndex].cardStatus = status;
            if (status === 'success') {
                storedProducts[productIndex].extractTime = product.extractTime;
            }
            localStorage.setItem(STORAGE_KEY, JSON.stringify(storedProducts));
        }
    }

    // 加载已存储的数据
    function loadStoredData() {
        updateProductCount();
        loadCardData();
    }

    // 开始批量提取手卡
    async function startBatchExtract() {
        const startIndex = parseInt(document.getElementById('start-index').value);
        const endIndex = parseInt(document.getElementById('end-index').value);
        const batchSize = parseInt(document.getElementById('batch-size').value) || 5;

        if (!startIndex || !endIndex) {
            layer.msg('请输入起始和结束序号');
            return;
        }

        if (startIndex < 1 || endIndex < 1) {
            layer.msg('序号必须大于0');
            return;
        }

        // 获取liveId
        const urlParams = new URLSearchParams(window.location.search);
        const liveId = urlParams.get('liveId');
        if (!liveId) {
            layer.msg('未找到liveId参数，请确保在直播编辑页面使用');
            return;
        }

        try {
            // 从localStorage获取产品数据
            const productData = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');

            // 如果localStorage中没有数据，提示用户先提取产品列表
            if (productData.length === 0) {
                layer.msg('请先提取产品列表');
                return;
            }

            // 根据序号区间筛选产品
            const productsToExtract = filterProductsByRange(productData, startIndex, endIndex);
            if (productsToExtract.length === 0) {
                layer.msg(`在序号区间 ${startIndex}-${endIndex} 内未找到产品`);
                return;
            }

            extractedProducts = productsToExtract;

            // 显示产品列表
            displayProductList(productsToExtract);

            // 开始批量提取手卡（支持并发）
            await batchExtractCardsAsync(liveId, batchSize);

        } catch (error) {
            console.error('批量提取失败:', error);
            layer.msg('批量提取失败: ' + error.message);
        }
    }

    // 开始批量提取手卡（线程池方式）
    async function startBatchExtractWithPool() {
        const startIndex = parseInt(document.getElementById('start-index').value);
        const endIndex = parseInt(document.getElementById('end-index').value);
        const poolSize = parseInt(document.getElementById('batch-size').value) || 20;

        if (!startIndex || !endIndex) {
            layer.msg('请输入起始和结束序号');
            return;
        }

        if (startIndex < 1 || endIndex < 1) {
            layer.msg('序号必须大于0');
            return;
        }

        // 获取liveId
        const urlParams = new URLSearchParams(window.location.search);
        const liveId = urlParams.get('liveId');
        if (!liveId) {
            layer.msg('未找到liveId参数，请确保在直播编辑页面使用');
            return;
        }

        try {
            // 从localStorage获取产品数据
            const productData = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');

            // 如果localStorage中没有数据，提示用户先提取产品列表
            if (productData.length === 0) {
                layer.msg('请先提取产品列表');
                return;
            }

            // 根据序号区间筛选产品
            const productsToExtract = filterProductsByRange(productData, startIndex, endIndex);
            if (productsToExtract.length === 0) {
                layer.msg(`在序号区间 ${startIndex}-${endIndex} 内未找到产品`);
                return;
            }

            extractedProducts = productsToExtract;

            // 显示产品列表
            displayProductList(productsToExtract);

            // 开始批量提取手卡（线程池方式）
            await batchExtractCardsWithPool(liveId, poolSize);

        } catch (error) {
            console.error('批量提取失败:', error);
            layer.msg('批量提取失败: ' + error.message);
        }
    }

    // 根据序号区间筛选产品
    function filterProductsByRange(productData, startIndex, endIndex) {
        const filtered = [];

        for (const product of productData) {
            const goodsIndex = parseInt(product.goodsIndex);
            if (goodsIndex >= Math.min(startIndex, endIndex) && goodsIndex <= Math.max(startIndex, endIndex)) {
                filtered.push(product);
            }
        }

        // 按序号排序
        filtered.sort((a, b) => parseInt(a.goodsIndex) - parseInt(b.goodsIndex));
        return filtered;
    }

    // 获取产品列表
    async function getProductList(liveId) {
        const h5Token = getH5Token();
        if (!h5Token) {
            throw new Error('未找到h5_tk token，请确保已登录淘宝');
        }

        const timestamp = Date.now();
        const appKey = '12574478';
        const api = 'mtop.taobao.dreamweb.live.item.all.query';
        const dataStr = JSON.stringify({
            "liveId": liveId,
            "searchType": "all",
            "pageNum": 1,
            "anchorSource": "pc"
        });

        const signString = `${h5Token}&${timestamp}&${appKey}&${dataStr}`;
        const sign = md5(signString);

        const baseUrl = 'https://h5api.m.taobao.com/h5/mtop.taobao.dreamweb.live.item.all.query/1.0/';
        const params = new URLSearchParams({
            jsv: '2.7.4',
            appKey: appKey,
            t: timestamp,
            sign: sign,
            api: api,
            jsonpIncPrefix: 'tbla_commodity_list',
            v: '1.0',
            preventFallback: 'true',
            type: 'jsonp',
            dataType: 'jsonp',
            callback: 'mtopjsonptbla_commodity_list' + Math.floor(Math.random() * 100),
            data: dataStr
        });

        const requestUrl = baseUrl + '?' + params.toString();
        const data = await makeJsonpRequest(requestUrl);

        if (!data || !data.data || !data.data.itemList) {
            throw new Error('响应数据格式错误');
        }

        return data.data.itemList.map((item, index) => ({
            itemId: item.itemId,
            title: item.itemName,
            price: item.itemPrice,
            commission: item.extendVal?.tcpCommission || '',
            goodsIndex: item.goodsIndex || (index + 1), // 使用API返回的序号或生成序号
            // 导出Excel需要的字段名
            itemName: item.itemName,
            itemPrice: item.itemPrice,
            tcpCommission: item.extendVal?.tcpCommission || '',
            tcpCommissionType: item.extendVal?.tcpCommissionType || '',
            categoryLevelOneName: item.categoryLevelOneName || ''
        }));
    }

    // 显示产品列表
    function displayProductList(products) {
        const gridContainer = document.getElementById('extract-grid');
        const progressElement = document.getElementById('extract-progress');

        gridContainer.innerHTML = '';
        progressElement.textContent = `0/${products.length}`;

        // 按序号倒序排列
        const sortedProducts = [...products].sort((a, b) => {
            const indexA = parseInt(a.goodsIndex) || 0;
            const indexB = parseInt(b.goodsIndex) || 0;
            return indexB - indexA; // 倒序
        });

        sortedProducts.forEach((product, index) => {
            const item = document.createElement('div');
            item.className = 'grid-item status-waiting';
            item.id = `grid-item-${index}`;
            item.dataset.productIndex = index;
            item.dataset.itemId = product.itemId;
            item.dataset.goodsIndex = product.goodsIndex || (index + 1);

            // 使用产品的真实序号
            const realIndex = product.goodsIndex || (index + 1);
            item.textContent = realIndex;

            // 添加点击事件（只有成功状态才能点击）
            item.addEventListener('click', () => {
                if (item.classList.contains('status-success')) {
                    showCardModal(product.itemId, realIndex);
                }
            });

            gridContainer.appendChild(item);
        });

        // 存储排序后的产品列表供其他函数使用
        extractedProducts = sortedProducts;
    }

    // 异步批量提取手卡
    async function batchExtractCardsAsync(liveId, batchSize = 5) {
        const cardClient = new CustomCardClient();
        const progressElement = document.getElementById('extract-progress');
        let completedCount = 0;
        let successCount = 0;

        // 创建任务队列
        const tasks = extractedProducts.map((product, index) => ({
            product,
            index,
            gridElement: document.getElementById(`grid-item-${index}`)
        }));

        // 分批处理
        for (let i = 0; i < tasks.length; i += batchSize) {
            const batch = tasks.slice(i, i + batchSize);

            // 并发处理当前批次
            const promises = batch.map(async (task) => {
                const { product, index, gridElement } = task;

                try {
                    // 更新状态为处理中
                    if (gridElement) {
                        gridElement.className = 'grid-item status-processing';
                    }

                    // 提取手卡
                    const cardData = await cardClient.getCardInfo(product, liveId);

                    if (cardData && cardData.script) {
                        // 成功
                        updateProductStatus(product, 'success', gridElement);
                        saveCardData(cardData);
                        successCount++;
                    } else {
                        // 失败
                        updateProductStatus(product, 'failed', gridElement);
                    }

                } catch (error) {
                    console.error(`提取第${index+1}个产品手卡失败:`, error);
                    updateProductStatus(product, 'failed', gridElement);
                }

                completedCount++;
                progressElement.textContent = `${completedCount}/${tasks.length}`;

                // 更新顶部统计
                updateProductCount();
            });

            // 等待当前批次完成
            await Promise.all(promises);

            // 批次间添加延迟
            if (i + batchSize < tasks.length) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        const failedCount = tasks.length - successCount;

        // 显示重新提取按钮（如果有失败的）
        const retryBtn = document.getElementById('retry-failed-btn');
        if (failedCount > 0 && retryBtn) {
            retryBtn.style.display = 'block';
        }

        layer.msg(`批量提取完成！成功提取 ${successCount} 个手卡，失败 ${failedCount} 个`);
    }

    // 线程池方式批量提取手卡
    async function batchExtractCardsWithPool(liveId, poolSize = 20) {
        const cardClient = new CustomCardClient();
        const progressElement = document.getElementById('extract-progress');

        let completedCount = 0;
        let successCount = 0;
        let currentIndex = 0;
        const totalTasks = extractedProducts.length;

        // 创建任务队列
        const tasks = extractedProducts.map((product, index) => ({
            product,
            index,
            gridElement: document.getElementById(`grid-item-${index}`),
            status: 'waiting' // waiting, processing, completed
        }));

        // 线程池：正在处理的任务
        const activePool = new Set();

        // 处理单个任务的函数
        const processTask = async (task) => {
            const { product, index, gridElement } = task;

            try {
                task.status = 'processing';

                // 更新状态为处理中
                if (gridElement) {
                    gridElement.className = 'grid-item status-processing';
                }



                // 提取手卡
                const cardData = await cardClient.getCardInfo(product, liveId);

                if (cardData && cardData.script) {
                    // 成功
                    updateProductStatus(product, 'success', gridElement);
                    saveCardData(cardData);
                    successCount++;
                } else {
                    // 失败
                    updateProductStatus(product, 'failed', gridElement);
                }

            } catch (error) {
                console.error(`提取第${index+1}个产品手卡失败:`, error);
                updateProductStatus(product, 'failed', gridElement);
            } finally {
                task.status = 'completed';
                completedCount++;
                progressElement.textContent = `${completedCount}/${totalTasks}`;

                // 更新顶部统计
                updateProductCount();

                // 从活跃池中移除
                activePool.delete(task);
            }
        };

        // 填充线程池的函数
        const fillPool = () => {
            while (activePool.size < poolSize && currentIndex < tasks.length) {
                const task = tasks[currentIndex];
                if (task.status === 'waiting') {
                    activePool.add(task);
                    processTask(task); // 不等待，让它异步执行
                }
                currentIndex++;
            }
        };

        // 初始填充线程池
        fillPool();

        // 监控线程池，当有任务完成时补充新任务
        while (completedCount < totalTasks) {
            // 等待一小段时间再检查
            await new Promise(resolve => setTimeout(resolve, 100));

            // 如果线程池有空位且还有待处理任务，则补充
            if (activePool.size < poolSize && currentIndex < tasks.length) {
                fillPool();
            }
        }

        const failedCount = totalTasks - successCount;

        // 显示重新提取按钮（如果有失败的）
        const retryBtn = document.getElementById('retry-failed-btn');
        if (failedCount > 0 && retryBtn) {
            retryBtn.style.display = 'block';
        }

        layer.msg(`线程池批量提取完成！成功提取 ${successCount} 个手卡，失败 ${failedCount} 个`);
    }

    // 保存手卡数据（以itemId为key，避免重复）
    function saveCardData(cardData) {
        try {
            // 获取现有的手卡数据对象
            const existingData = JSON.parse(localStorage.getItem(CARD_STORAGE_KEY) || '{}');

            // 以itemId为key保存，新数据覆盖旧数据
            existingData[cardData.itemId] = cardData;

            localStorage.setItem(CARD_STORAGE_KEY, JSON.stringify(existingData));

            // 更新内存中的数组（用于兼容现有代码）
            const existingIndex = cardDataArray.findIndex(item => item.itemId === cardData.itemId);
            if (existingIndex !== -1) {
                cardDataArray[existingIndex] = cardData;
            } else {
                cardDataArray.push(cardData);
            }
        } catch (error) {
            console.error('保存手卡数据失败:', error);
        }
    }

    // 加载手卡数据
    function loadCardData() {
        try {
            const saved = localStorage.getItem(CARD_STORAGE_KEY);
            if (saved) {
                const cardDataObject = JSON.parse(saved);
                // 转换为数组格式（用于兼容现有代码）
                cardDataArray = Object.values(cardDataObject);
            }
        } catch (error) {
            console.error('加载手卡数据失败:', error);
            cardDataArray = [];
        }
    }

    // 显示手卡Modal
    function showCardModal(itemId, goodsIndex) {
        const cardData = cardDataArray.find(card => card.itemId === itemId);
        if (!cardData || !cardData.script) {
            layer.msg('未找到该产品的手卡数据');
            return;
        }

        // 创建Modal HTML
        const modalHtml = `
            <div class="card-modal" id="card-modal">
                <div class="card-modal-content">
                    <div class="card-modal-header">
                        <div class="card-modal-title">产品序号: ${goodsIndex} - 手卡内容</div>
                        <button class="card-modal-close" id="card-modal-close-btn">&times;</button>
                    </div>
                    <div class="card-content">${cardData.script}</div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 绑定关闭事件
        const modal = document.getElementById('card-modal');
        const closeBtn = document.getElementById('card-modal-close-btn');

        // 点击关闭按钮
        closeBtn.addEventListener('click', closeCardModal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeCardModal();
            }
        });
    }

    // 关闭手卡Modal
    function closeCardModal() {
        const modal = document.getElementById('card-modal');
        if (modal) {
            modal.remove();
        }
    }

    // 重新提取失败的产品
    async function retryFailedExtraction() {
        const failedItems = document.querySelectorAll('.grid-item.status-failed');
        if (failedItems.length === 0) {
            layer.msg('没有需要重新提取的失败产品');
            return;
        }

        // 获取失败产品的数据
        const failedProducts = [];
        const failedTasks = [];

        failedItems.forEach(item => {
            const productIndex = parseInt(item.dataset.productIndex);
            if (extractedProducts[productIndex]) {
                failedProducts.push(extractedProducts[productIndex]);
                failedTasks.push({
                    product: extractedProducts[productIndex],
                    index: productIndex,
                    gridElement: item
                });
            }
        });

        if (failedProducts.length === 0) {
            layer.msg('未找到失败的产品数据');
            return;
        }

        // 获取liveId
        const urlParams = new URLSearchParams(window.location.search);
        const liveId = urlParams.get('liveId');
        if (!liveId) {
            layer.msg('未找到liveId参数');
            return;
        }

        // 获取并发数设置
        const batchSize = parseInt(document.getElementById('batch-size').value) || 5;

        layer.msg(`开始重新提取 ${failedProducts.length} 个失败的产品...`);

        try {
            // 直接调用批量提取逻辑，只处理失败的任务
            await batchExtractFailedTasks(failedTasks, liveId, batchSize);

            // 检查是否还有失败的
            const stillFailedItems = document.querySelectorAll('.grid-item.status-failed');
            const retryBtn = document.getElementById('retry-failed-btn');
            if (stillFailedItems.length === 0 && retryBtn) {
                retryBtn.style.display = 'none';
            }
        } catch (error) {
            console.error('重新提取失败:', error);
            layer.msg('重新提取过程中出现错误');
        }
    }

    // 批量提取失败的任务
    async function batchExtractFailedTasks(tasks, liveId, batchSize) {
        const cardClient = new CustomCardClient();
        let completedCount = 0;
        let successCount = 0;

        // 分批处理
        for (let i = 0; i < tasks.length; i += batchSize) {
            const batch = tasks.slice(i, i + batchSize);

            // 并发处理当前批次
            const promises = batch.map(async (task) => {
                const { product, index, gridElement } = task;

                try {
                    // 更新状态为处理中
                    if (gridElement) {
                        gridElement.className = 'grid-item status-processing';
                    }

                    // 提取手卡
                    const cardData = await cardClient.getCardInfo(product, liveId);

                    if (cardData && cardData.script) {
                        // 成功
                        updateProductStatus(product, 'success', gridElement);
                        saveCardData(cardData);
                        successCount++;
                    } else {
                        // 失败
                        updateProductStatus(product, 'failed', gridElement);
                    }

                } catch (error) {
                    console.error(`重新提取第${index+1}个产品手卡失败:`, error);
                    if (gridElement) {
                        gridElement.className = 'grid-item status-failed';
                    }
                }

                completedCount++;
                // 更新顶部统计
                updateProductCount();
            });

            // 等待当前批次完成
            await Promise.all(promises);

            // 批次间添加延迟
            if (i + batchSize < tasks.length) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        const failedCount = tasks.length - successCount;
        layer.msg(`重新提取完成！成功提取 ${successCount} 个手卡，失败 ${failedCount} 个`);
    }
    
    // 手卡信息获取器
    class CustomCardClient {
        constructor() {
            this.appKey = "12574478";
            this.jsv = "2.7.4";
            this.apiName = "mtop.tblive.portal.item.card.user.token";
            this.apiVersion = "1.0";
        }

        // 获取h5_tk token用于签名
        getH5Token() {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === '_m_h5_tk') {
                    return value.split('_')[0];
                }
            }
            return "";
        }

        // 获取用户token
        async getUserToken(liveId) {
            const timestamp = Date.now().toString();
            const dataStr = JSON.stringify({"liveId": liveId});

            const h5Token = this.getH5Token();
            const signString = `${h5Token}&${timestamp}&${this.appKey}&${dataStr}`;
            const sign = md5(signString);

            const params = new URLSearchParams({
                'jsv': this.jsv,
                'appKey': this.appKey,
                't': timestamp,
                'sign': sign,
                'api': this.apiName,
                'v': this.apiVersion,
                'preventFallback': 'true',
                'type': 'jsonp',
                'dataType': 'jsonp',
                'callback': 'mtopjsonp189',
                'data': dataStr
            });

            const url = `https://h5api.m.taobao.com/h5/${this.apiName}/${this.apiVersion}/?${params}`;

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const text = await response.text();
                    const match = text.match(/mtopjsonp189\((.*)\)/);
                    if (match) {
                        const data = JSON.parse(match[1]);
                        if (data.ret && data.ret[0].includes('SUCCESS')) {
                            const token = data.data?.result;
                            if (token) {

                                return token;
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('获取手卡token失败:', error);
            }
            return null;
        }

        // 获取手卡信息（简化版，只返回数据）
        async getCardInfo(productInfo, liveId) {
            const token = await this.getUserToken(liveId);
            if (!token) {
                console.log("获取手卡token失败");
                return null;
            }

            // 验证必要参数
            if (!productInfo.itemId) {
                console.log("未找到商品ID");
                return null;
            }



            // 构建手卡请求参数
            const params = new URLSearchParams({
                'itemId': productInfo.itemId,
                'liveId': liveId,
                'type': '1',
                'subType': '1',
                'isRenew': 'false',
                'scene': 'normal',
                'reqKey': token,
                'sceneCode': 'beforeLivebatch',
                'trackId': Math.random().toString(36).substring(2, 11),
                'isFilterSavedScript': 'true'
            });

            const url = `https://tblive.taobao.com/api/sse/script/query?${params}`;

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Accept': 'text/event-stream',
                        'Cache-Control': 'no-cache'
                    }
                });

                if (!response.ok) {
                    console.log(`手卡SSE连接失败: ${response.status}`);
                    return null;
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullScript = '';

                try {
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value, { stream: true });
                        const lines = chunk.split('\n');

                        for (let line of lines) {
                            if (line.trim()) {
                                const [dataType, content] = this.parseSSEData(line);

                                if (dataType === 'data' && content) {
                                    try {
                                        const jsonData = JSON.parse(content);

                                        const useCache = jsonData.useCache || false;
                                        const finished = jsonData.finished || false;
                                        const script = jsonData.script || '';

                                        if (script) {
                                            fullScript = script;

                                            if (useCache || finished) {
                                                return {
                                                    itemId: productInfo.itemId,
                                                    goodsIndex: productInfo.goodsIndex,
                                                    title: productInfo.title,
                                                    price: productInfo.price,
                                                    commission: productInfo.commission,
                                                    commissionType: productInfo.commissionType,
                                                    script: fullScript,
                                                    extractTime: new Date().toLocaleString()
                                                };
                                            }
                                        }

                                    } catch (e) {

                                    }
                                }
                            }
                        }
                    }

                    // 如果循环结束但有脚本内容，返回数据
                    if (fullScript) {
                        return {
                            itemId: productInfo.itemId,
                            goodsIndex: productInfo.goodsIndex,
                            title: productInfo.title,
                            price: productInfo.price,
                            commission: productInfo.commission,
                            commissionType: productInfo.commissionType,
                            script: fullScript,
                            extractTime: new Date().toLocaleString()
                        };
                    }

                } finally {
                    reader.releaseLock();
                }

            } catch (error) {
                console.error("手卡连接异常:", error);
                return null;
            }

            return null;
        }

        // 解析SSE数据
        parseSSEData(line) {
            line = line.trim();
            if (!line) return [null, null];

            if (line.startsWith('data:')) {
                return line.startsWith('data: ')
                    ? ['data', line.substring(6)]
                    : ['data', line.substring(5)];
            } else if (line.startsWith('event:')) {
                return line.startsWith('event: ')
                    ? ['event', line.substring(7)]
                    : ['event', line.substring(6)];
            } else if (line.startsWith('id:')) {
                return line.startsWith('id: ')
                    ? ['id', line.substring(4)]
                    : ['id', line.substring(3)];
            } else {
                return ['unknown', line];
            }
        }
    }

    // 一键设置功能
    async function quickSetup() {
        try {
            // 获取页面上的第一个序号输入框
            const sortInput = document.querySelector('input[data-tblalog-id="sort_input"]');
            if (!sortInput || !sortInput.value) {
                layer.msg('未找到页面序号信息，请确保在淘宝直播商品页面');
                return;
            }

            const maxIndex = parseInt(sortInput.value);
            if (isNaN(maxIndex) || maxIndex <= 0) {
                layer.msg('页面序号信息无效');
                return;
            }

            // 计算起始和结束序号
            const startIndex = maxIndex;  // 第一个框显示最大数
            const endIndex = Math.max(1, maxIndex - 199 );  // 第二个框显示较小数

            // 设置输入框值
            document.getElementById('start-index').value = startIndex;
            document.getElementById('end-index').value = endIndex;
            document.getElementById('batch-size').value = 20;

            layer.msg(`已设置序号区间: ${endIndex}-${startIndex}，并发数: 20`);

            // 自动渲染产品列表（注意参数顺序：小数在前，大数在后）
            await renderProductList(endIndex, startIndex);

        } catch (error) {
            console.error('一键设置失败:', error);
            layer.msg('一键设置失败: ' + error.message);
        }
    }

    // 渲染产品列表
    async function renderProductList(startIndex, endIndex) {
        try {
            // 获取liveId
            const urlParams = new URLSearchParams(window.location.search);
            const liveId = urlParams.get('liveId');
            if (!liveId) {
                layer.msg('未找到liveId参数');
                return;
            }

            // 先尝试从localStorage获取已有的产品数据
            let productData = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');

            // 如果localStorage中没有数据，则从API获取
            if (productData.length === 0) {
                productData = await getProductList(liveId);
                if (!productData || productData.length === 0) {
                    layer.msg('未获取到产品数据');
                    return;
                }
                // 保存到localStorage
                localStorage.setItem(STORAGE_KEY, JSON.stringify(productData));
            }

            // 根据序号区间筛选产品
            const productsToRender = filterProductsByRange(productData, startIndex, endIndex);

            if (productsToRender.length === 0) {
                layer.msg(`在序号区间 ${startIndex}-${endIndex} 内未找到产品`);
                return;
            }

            // 显示产品列表
            displayProductList(productsToRender);

            layer.msg(`已渲染 ${productsToRender.length} 个产品到列表中`);

        } catch (error) {
            console.error('渲染产品列表失败:', error);
            layer.msg('渲染产品列表失败: ' + error.message);
        }
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initPlugin);
    } else {
        initPlugin();
    }

})();
